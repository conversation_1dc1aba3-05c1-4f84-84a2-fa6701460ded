# ==================== ICT TRADING SYSTEM v8.1 - FLEXIBLE & COMPLETE ====================

import pandas as pd
import numpy as np
import warnings
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import traceback
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches
import os
from smartmoneyconcepts.smc import smc
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)

print("ICT Trading System v8.2 - Complete with Enhanced Chartingssssss")
print("=" * 70)

try:
    from smartmoneyconcepts.smc import smc
    print("✅ SMC library imported successfully")
    # Test with sample data
    test_df = pd.DataFrame({
        'open': [100, 101, 102, 103, 104],
        'high': [101, 102, 103, 104, 105],
        'low': [99, 100, 101, 102, 103],
        'close': [100.5, 101.5, 102.5, 103.5, 104.5]
    })
    test_swings = smc.swing_highs_lows(test_df, swing_length=2)
    print("✅ SMC functions working correctly")
except Exception as e:
    print(f"❌ SMC import error: {e}")
    
# ==================== SIGNAL DATACLASS ====================

@dataclass
class ICTSignal:
    signal_type: str  # 'ict_sequence', 'raid_fvg', 'displacement_fvg', 'fvg_only'
    direction: str    # 'bullish', 'bearish'
    entry_price: float
    stop_loss: float
    take_profit: float
    timeframe: str
    confidence: float
    timestamp: pd.Timestamp
    setup_details: dict = None

# ==================== ICT TRADING SYSTEM - ORGANIZED CONFIG ====================

# 🌟 STRATEGY ENABLERS - Your Main Control Panel
STRATEGY_SETTINGS = {
    'generate_raid_fvg': True,          # 🔥 YOUR MAIN STRATEGY
    # 'generate_displacement_fvg': False,   # 🔥 CURRENTLY ENABLED  
   

}   

# 📊 CORE SYSTEM SETTINGS (Apply to ALL strategies)
CORE_SETTINGS = {
    # Data & Timeframes
    'candle_data_path': 'bybit_btcusdt_price_1m_2020-05-01_2025-03-08.csv',
    'backtest_start': '2025-02-01',
    'backtest_end': '2025-02-28',
    'crypto_symbol': 'BTC',
    'ltf_timeframe': '5m',          # Trade execution timeframe
    'htf_timeframe': '15m',         # Bias analysis timeframe
    
    # Risk Management
    'starting_balance': 5000,
    'risk_per_trade': 0.45,         # % of balance to risk per trade
    'fee_rate': 0.0006,             # 0.06% per side
    'risk_management_mode': 'compounded',  # 'fixed' or 'compounded'
    
    # Trade Management (Used by ALL strategies)
    'min_rr_ratio': 1.5,            # Minimum risk:reward ratio
    'stop_loss_method': 'fvg_based', # 'fvg_based', 'swing_based', 'atr_based'
    'take_profit_method': 'fixed_r', # 'fixed_r', 'swing_liquidity'
    'r_multiple_target': 3.5,       # Target R-multiple for fixed_r method
    'allow_neutral_trades': False,  # True = trade on htf bias neutral, False = skip neutral
     
    # HTF Analysis (Used by ALL strategies)
    'use_htf_bias': True,           # Enable HTF bias analysis # YES - enables HTF analysis,Enables HTF (15m) analysis for key levels and bias
    # 'use_htf_filter': True,
    # 'use_trend_filter': True,       # Filter trades by HTF trend, When True, only takes trades aligned with HTF trend,when true with HTF bias determines trend → trend filter enforces it
    # 'trend_lookback': 30,            # Bars for trend calculation, only ICT strategy?
    'htf_lookback_candles': 100,      # Candles to look back for HTF bias
    

    
    # System Controls
    'max_trades_per_day': 15,
    'debug_mode': True,
    'plot_charts': True,
    'plot_htf_charts': True,
    'max_charts': 20,
        
    # 📊 OUTPUT SETTINGS
    'save_individual_trades': True,
    'save_summary_metrics': True,  
}


# 📈 SHARED FVG SETTINGS (Used by raid_fvg AND displacement_fvg)
FVG_SETTINGS = {
    # 'fvg_min_size_pct': 3.5,         # FVG min size as % of ATR (2.5% = visible gaps, 1.5% = smaller gaps, 4.0% = only large gaps)
    # 'body_ratio_threshold': 0.75,     # Min body/total ratio for strong candles
    'fvg_entry_method': 'touch',  # 'touch' or 'midpoint'
    

    
    # 🆕 FVG ENTRY TIMING (GENERAL - applies to all FVG strategies)
    # 'fvg_entry_timeout_bars': 20,    # Max bars to wait for FVG entry after signal.  #NOT USED
    # 'fvg_entry_min_bars': 2,         # Min bars before entering FVG (avoid immediate entries) #NOT USED 
    

}

# 🏗️ MARKET STRUCTURE SETTINGS (Used by both strategies)
STRUCTURE_SETTINGS = {
  
    'swing_stop_buffer_pct': 0.2,   # Buffer % beyond swing for stops
    'min_stop_distance_pct': 0.1,   # Min stop distance as % of price
    'max_stop_distance_pct': 3.0,   # Max stop distance as % of price
    'max_fee_pct_of_risk': 60.0,    # Max fees as % of risk amount

      # ADD THESE NEW SETTINGS:
    'swing_lookback_period': 30,    # How many bars to look back for swings
    'atr_period': 14,               # For ATR-based stops
    'atr_multiplier': 1.5,          # ATR multiplier for stop distance
    'stop_loss_pct': 2.0,           # Fallback percentage stop


}

# ⚡ RAID_FVG STRATEGY CONFIG (Only used when generate_raid_fvg = True)
RAID_FVG_CONFIG = {
    # Liquidity Detection
    'liquidity_buffer_pct': 0.05,    # Buffer % beyond swing for raid detection
    
    # FVG Timing After Raid
   
    'max_bars_after_raid': 50,       # Max bars to look for FVG after raid
    
        # SMC-specific parameters
    'smc_swing_length': 3,              # Swing detection sensitivity
    'smc_liquidity_range': 0.02,        # 1% range for liquidity clustering
    'smc_fvg_window': 50,               # Candles to look for FVG after raid
}



# ==================== COMBINED CONFIG (DON'T MODIFY) ====================
# This automatically combines all the above into one config dict
config = {}
config.update(STRATEGY_SETTINGS)
config.update(CORE_SETTINGS)  
config.update(FVG_SETTINGS)
config.update(STRUCTURE_SETTINGS)
config.update(RAID_FVG_CONFIG)
# config.update(DISPLACEMENT_CONFIG)



# ==================== DATA LOADING ==================== 

def load_and_prepare_data(filepath: str) -> pd.DataFrame:
    """Load CSV data with validation - FIXED: Ensure datetime index"""
    print(f"\n📁 Loading: {filepath}")
    
    try:
        df = pd.read_csv(filepath)
        print(f"✅ Loaded {len(df):,} rows")
        
        # Find datetime column
        datetime_col = None
        for col in ['datetime', 'timestamp', 'time', 'open_time']:
            if col in df.columns:
                datetime_col = col
                break
        
        if datetime_col is None:
            raise ValueError(f"No datetime column found")
        
        # Convert to datetime and set index
        df['datetime'] = pd.to_datetime(df[datetime_col])
        df = df.set_index('datetime').sort_index()
        
        # Remove the original datetime column if it's different from 'datetime'
        if datetime_col != 'datetime' and datetime_col in df.columns:
            df = df.drop(columns=[datetime_col])
        
        # Validate OHLC columns
        required = ['open', 'high', 'low', 'close']
        if not all(col in df.columns for col in required):
            raise ValueError(f"Missing OHLC columns")
        
        print(f"📈 Date range: {df.index.min()} to {df.index.max()}")
        print(f"📊 Index type: {type(df.index).__name__}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
        

def resample_data(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """Resample to target timeframe"""
    freq_map = {
        '1m': '1T', '5m': '5T', '15m': '15T', '30m': '30T',
        '1h': '1H', '4h': '4H', '1d': '1D'
    }
    freq = freq_map.get(timeframe, timeframe)
    
    resampled = df.resample(freq).agg({
        'open': 'first',
        'high': 'max',
        'low': 'min', 
        'close': 'last'
    }).dropna()

    resampled = resampled.reset_index() # put back datetime
    resampled = resampled.set_index('datetime')

    return resampled






# ==================== CALCULATE TAKE PROFIT AND STOP LOSS  ====================


def calculate_stop_loss_CORRECTED(entry_price, direction, fvg_high, fvg_low, data, candle_index):
    """
    CORRECTED: Calculate stop loss based on method in config
    """
    stop_loss_method = config.get('stop_loss_method', 'swing_based')
    
    if stop_loss_method == 'fvg_based':
        # MODIFIED: Check if this is a displacement_fvg signal to use HTF FVG
        # Look for HTF FVG in setup_details (passed through fvg_high/fvg_low parameters)
        # For displacement_fvg, fvg_high/fvg_low should be HTF FVG levels
        if direction == 'bullish':
            stop = fvg_low * (1 - config.get('swing_stop_buffer_pct', 0.2) / 100)
        else:
            stop = fvg_high * (1 + config.get('swing_stop_buffer_pct', 0.2) / 100)
            
    elif stop_loss_method == 'swing_based':
        # FIXED: Limit how far back we look for swings
        lookback_period = config.get('swing_lookback_period', 30)
        start_idx = max(0, candle_index - lookback_period)
        end_idx = candle_index
        
        # Get current price for distance check
        current_price = data['close'].iloc[candle_index]
        
        if direction == 'bullish':
            # Find swing lows
            swing_lows = []
            
            # Simple swing detection - look for local lows
            for i in range(start_idx + 3, end_idx - 3):
                if (data['low'].iloc[i] < data['low'].iloc[i-1] and 
                    data['low'].iloc[i] < data['low'].iloc[i-2] and
                    data['low'].iloc[i] < data['low'].iloc[i+1] and
                    data['low'].iloc[i] < data['low'].iloc[i+2]):
                    
                    # Check distance from current price (max 3% away)
                    distance_pct = (current_price - data['low'].iloc[i]) / current_price * 100
                    if distance_pct <= 3.0:  # Only use swings within 3%
                        swing_lows.append(data['low'].iloc[i])
            
            if swing_lows:
                # Use the lowest recent swing
                swing_low = min(swing_lows)
            else:
                # No good swings found, use recent low
                swing_low = data['low'].iloc[max(start_idx, end_idx-10):end_idx].min()
            
            stop = swing_low * (1 - config.get('swing_stop_buffer_pct', 0.2) / 100)
        else:
            # Find swing highs
            swing_highs = []
            
            for i in range(start_idx + 3, end_idx - 3):
                if (data['high'].iloc[i] > data['high'].iloc[i-1] and 
                    data['high'].iloc[i] > data['high'].iloc[i-2] and
                    data['high'].iloc[i] > data['high'].iloc[i+1] and
                    data['high'].iloc[i] > data['high'].iloc[i+2]):
                    
                    # Check distance from current price (max 3% away)
                    distance_pct = (data['high'].iloc[i] - current_price) / current_price * 100
                    if distance_pct <= 3.0:
                        swing_highs.append(data['high'].iloc[i])
            
            if swing_highs:
                # Use the highest recent swing
                swing_high = max(swing_highs)
            else:
                # No good swings found, use recent high
                swing_high = data['high'].iloc[max(start_idx, end_idx-10):end_idx].max()
            
            stop = swing_high * (1 + config.get('swing_stop_buffer_pct', 0.2) / 100)
            
    elif stop_loss_method == 'atr_based':
        atr_period = config.get('atr_period', 14)
        if candle_index >= atr_period:
            # Calculate ATR
            high_low = data['high'].iloc[candle_index-atr_period:candle_index] - data['low'].iloc[candle_index-atr_period:candle_index]
            high_close = abs(data['high'].iloc[candle_index-atr_period:candle_index] - data['close'].iloc[candle_index-atr_period-1:candle_index-1])
            low_close = abs(data['low'].iloc[candle_index-atr_period:candle_index] - data['close'].iloc[candle_index-atr_period-1:candle_index-1])
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.mean()
            
            if direction == 'bullish':
                stop = entry_price - (atr * config.get('atr_multiplier', 1.5))
            else:
                stop = entry_price + (atr * config.get('atr_multiplier', 1.5))
        else:
            # Fallback to percentage if not enough data
            if direction == 'bullish':
                stop = entry_price * (1 - config.get('stop_loss_pct', 2.0) / 100)
            else:
                stop = entry_price * (1 + config.get('stop_loss_pct', 2.0) / 100)
    else:
        # Default percentage_based
        if direction == 'bullish':
            stop = entry_price * (1 - config.get('stop_loss_pct', 2.0) / 100)
        else:
            stop = entry_price * (1 + config.get('stop_loss_pct', 2.0) / 100)
    
    return stop


def calculate_take_profit_CORRECTED(entry_price, stop_loss, direction, risk_amount_actual, fvg_high, fvg_low, data=None, candle_index=None):
    """
    CORRECTED: Take profit calculation with proper compounded logic
    
    fixed_r: Sets TP at R-multiple distance (compounded mode uses current balance for position sizing)
    swing_liquidity: Targets actual market structure levels
    
    Fees are deducted in final P&L calculation, not from TP target
    """
    take_profit_method = config.get('take_profit_method', 'fixed_r')
    
    if take_profit_method == 'fixed_r':
        r_multiple_target = config.get('r_multiple_target', 4)
        risk_mode = config.get('risk_management_mode', 'compounded')
        
        if risk_mode == 'fixed':
            # FIXED MODE: Simple R-multiple distance
            stop_distance = abs(entry_price - stop_loss)
            
            if direction == 'bullish':
                take_profit = entry_price + (stop_distance * r_multiple_target)
            else:
                take_profit = entry_price - (stop_distance * r_multiple_target)
            
            return take_profit
            
        else:  # compounded mode
            # COMPOUNDED MODE: Account for varying position sizes
            # TP distance stays the same, but we need to consider the actual risk amount
            stop_distance_price = abs(entry_price - stop_loss)
            
            # Position size based on current balance (risk_amount_actual)
            position_size = risk_amount_actual / stop_distance_price
            
            # Set TP at R-multiple distance (no fee adjustment to TP level)
            if direction == 'bullish':
                take_profit = entry_price + (stop_distance_price * r_multiple_target)
            else:
                take_profit = entry_price - (stop_distance_price * r_multiple_target)
                
            return take_profit
        
    elif take_profit_method == 'swing_liquidity':
        # SWING_BASED: Target actual market structure levels
        
        if data is None:
            # Fallback if no data provided
            stop_distance = abs(entry_price - stop_loss)
            if direction == 'bullish':
                take_profit = entry_price + (stop_distance * config.get('r_multiple_target', 4))
            else:
                take_profit = entry_price - (stop_distance * config.get('r_multiple_target', 4))
            return take_profit
        
        # Get market structure
        df_structure = identify_market_structure(data)
        
        if direction == 'bullish':
            # Target next swing high above entry
            swing_highs = df_structure['swing_high'].dropna()
            target_levels = [level for level in swing_highs if level > entry_price]
            
            if target_levels:
                # Target the next significant liquidity level
                sorted_targets = sorted(target_levels)
                next_liquidity = sorted_targets[0]  # First swing high above entry
                take_profit = next_liquidity * 0.99  # Just before the level
                
                # Ensure minimum R:R ratio
                min_distance = (entry_price - stop_loss) * config.get('min_rr_ratio', 1.5)
                min_tp = entry_price + min_distance
                if take_profit < min_tp:
                    take_profit = min_tp
                    
            else:
                # No swing highs found - fallback to fixed_r
                stop_distance = abs(entry_price - stop_loss)
                take_profit = entry_price + (stop_distance * config.get('r_multiple_target', 4))
                
        else:  # bearish
            # Target next swing low below entry
            swing_lows = df_structure['swing_low'].dropna()
            target_levels = [level for level in swing_lows if level < entry_price]
            
            if target_levels:
                # Target the next significant liquidity level
                sorted_targets = sorted(target_levels, reverse=True)
                next_liquidity = sorted_targets[0]  # First swing low below entry
                take_profit = next_liquidity * 1.01  # Just above the level
                
                # Ensure minimum R:R ratio
                min_distance = (stop_loss - entry_price) * config.get('min_rr_ratio', 1.5)
                min_tp = entry_price - min_distance
                if take_profit > min_tp:
                    take_profit = min_tp
                    
            else:
                # No swing lows found - fallback to fixed_r
                stop_distance = abs(entry_price - stop_loss)
                take_profit = entry_price - (stop_distance * config.get('r_multiple_target', 4))
        
        return take_profit
        
    else:
        # Default percentage-based (fallback)
        if direction == 'bullish':
            take_profit = entry_price * (1 + config.get('take_profit_pct', 8.0) / 100)
        else:
            take_profit = entry_price * (1 - config.get('take_profit_pct', 8.0) / 100)
            
        return take_profit
    


def calculate_position_size_with_fee_constraint(entry_price, stop_loss, risk_amount, direction='bullish'):
    """
    Calculate position size ensuring fees don't exceed max% of risk amount
    FIXED: Corrected fee calculation that doesn't over-restrict trades
    """
    fee_rate = config.get('fee_rate', 0.0006)
    max_fee_pct_of_risk = config.get('max_fee_pct_of_risk', 10.0) / 100
    
    stop_distance = abs(entry_price - stop_loss)
    if stop_distance == 0:
        return 0, risk_amount, 0
    
    # Calculate position size based on risk
    position_size = risk_amount / stop_distance
    
    # Calculate fees (entry + exit)
    entry_fee = entry_price * position_size * fee_rate
    exit_fee = stop_loss * position_size * fee_rate
    total_fees = entry_fee + exit_fee
    
    # FIXED: Only reject if fees are WAY too high (not just slightly over)
    # This prevents rejecting trades with 10.1% when limit is 10%
    if total_fees > risk_amount * max_fee_pct_of_risk * 1.2:  # 20% tolerance
        # Adjust position size
        target_fees = risk_amount * max_fee_pct_of_risk
        avg_price = (entry_price + stop_loss) / 2
        adjusted_position = target_fees / (2 * avg_price * fee_rate)
        
        return adjusted_position, adjusted_position * stop_distance, target_fees
    
    return position_size, risk_amount, total_fees

    
# ==================== IMPROVED FVG DETECTION WITH SEPARATE THRESHOLDS ====================


def identify_market_structure(data: pd.DataFrame, lookback: int = 50) -> pd.DataFrame:
    """
    FIXED: Proper swing detection without lookahead bias and better trend identification
    """
    df = data.copy()
    
    # Initialize columns
    df['swing_high'] = np.nan
    df['swing_low'] = np.nan
    df['structure'] = 'neutral'
    df['trend'] = 'neutral'
    
    # Get swing length from config
    swing_length = config.get('swing_length', 10)
    
    # FIXED: Proper swing detection without lookahead
    for i in range(swing_length, len(df) - swing_length):
        # For swing high: current high must be highest in the window
        window_high = df['high'].iloc[i-swing_length:i+swing_length+1]
        if df['high'].iloc[i] == window_high.max() and window_high.value_counts()[df['high'].iloc[i]] == 1:
            df.iloc[i, df.columns.get_loc('swing_high')] = df['high'].iloc[i]
        
        # For swing low: current low must be lowest in the window  
        window_low = df['low'].iloc[i-swing_length:i+swing_length+1]
        if df['low'].iloc[i] == window_low.min() and window_low.value_counts()[df['low'].iloc[i]] == 1:
            df.iloc[i, df.columns.get_loc('swing_low')] = df['low'].iloc[i]
    
    # FIXED: Better trend identification based on swing progression
    last_swing_high = None
    last_swing_low = None
    prev_swing_high = None
    prev_swing_low = None
    
    for i in range(len(df)):
        # Update swing tracking
        if not pd.isna(df.iloc[i]['swing_high']):
            prev_swing_high = last_swing_high
            last_swing_high = (i, df.iloc[i]['swing_high'])
            
        if not pd.isna(df.iloc[i]['swing_low']):
            prev_swing_low = last_swing_low
            last_swing_low = (i, df.iloc[i]['swing_low'])
        
        # Determine trend based on swing progression
        if last_swing_high and last_swing_low and prev_swing_high and prev_swing_low:
            # Higher highs and higher lows = bullish
            if (last_swing_high[1] > prev_swing_high[1] and 
                last_swing_low[1] > prev_swing_low[1]):
                df.iloc[i, df.columns.get_loc('trend')] = 'bullish'
                df.iloc[i, df.columns.get_loc('structure')] = 'bullish'
                
            # Lower highs and lower lows = bearish
            elif (last_swing_high[1] < prev_swing_high[1] and 
                  last_swing_low[1] < prev_swing_low[1]):
                df.iloc[i, df.columns.get_loc('trend')] = 'bearish'
                df.iloc[i, df.columns.get_loc('structure')] = 'bearish'
                
            # Mixed structure = neutral but check momentum
            else:
                # Use price action momentum as tiebreaker
                if i >= 20:
                    sma20 = df['close'].iloc[i-20:i].mean()
                    sma50 = df['close'].iloc[max(0,i-50):i].mean() if i >= 50 else sma20
                    
                    if df['close'].iloc[i] > sma20 and sma20 > sma50:
                        df.iloc[i, df.columns.get_loc('trend')] = 'bullish'
                    elif df['close'].iloc[i] < sma20 and sma20 < sma50:
                        df.iloc[i, df.columns.get_loc('trend')] = 'bearish'
                    else:
                        df.iloc[i, df.columns.get_loc('trend')] = 'neutral'
                else:
                    df.iloc[i, df.columns.get_loc('trend')] = 'neutral'
                    
                df.iloc[i, df.columns.get_loc('structure')] = df.iloc[i]['trend']
        
        # If not enough swings yet, use simple momentum
        elif i >= 20:
            close_20_ago = df['close'].iloc[i-20]
            pct_change = (df['close'].iloc[i] - close_20_ago) / close_20_ago * 100
            
            if pct_change > 2:
                df.iloc[i, df.columns.get_loc('trend')] = 'bullish'
            elif pct_change < -2:
                df.iloc[i, df.columns.get_loc('trend')] = 'bearish'
            else:
                df.iloc[i, df.columns.get_loc('trend')] = 'neutral'
                
            df.iloc[i, df.columns.get_loc('structure')] = df.iloc[i]['trend']
    
    return df


def find_ltf_fvg_smc(ltf_data: pd.DataFrame, raid_time: pd.Timestamp, 
                     raid_type: str, fvg_window: int = 10) -> List[Dict]:
    """
    Find LTF FVGs after HTF liquidity raid using SMC library
    
    Returns list of FVG dictionaries
    """
    # Find the index of raid time in LTF data
    raid_ltf_idx = None
    for i, time in enumerate(ltf_data.index):
        if time >= raid_time:
            raid_ltf_idx = i
            break
    
    if raid_ltf_idx is None or raid_ltf_idx >= len(ltf_data) - fvg_window:
        return []
    
    # Look for FVGs in the window after raid
    search_start = raid_ltf_idx
    search_end = min(raid_ltf_idx + fvg_window, len(ltf_data))
    
    # Get window data
    window_data = ltf_data.iloc[search_start:search_end]
    if len(window_data) < 3:
        return []
    
    # Detect FVGs using SMC
    fvg_data = smc.fvg(window_data[['open', 'high', 'low', 'close']])
    
    # Filter for appropriate FVG type
    valid_fvgs = []
    
    for i in range(len(fvg_data)):
        if pd.notna(fvg_data['FVG'].iloc[i]):
            fvg_value = fvg_data['FVG'].iloc[i]
            
            # Match FVG direction to raid type
            if (raid_type == 'bearish' and fvg_value == -1) or \
               (raid_type == 'bullish' and fvg_value == 1):
                
                # Get FVG boundaries
                fvg_top = fvg_data['Top'].iloc[i]
                fvg_bottom = fvg_data['Bottom'].iloc[i]
                
                # Calculate actual index in original data
                actual_idx = search_start + i
                
                valid_fvgs.append({
                    'fvg_time': window_data.index[i],
                    'fvg_idx': actual_idx,
                    'fvg_type': 'bearish' if fvg_value == -1 else 'bullish',
                    'fvg_top': fvg_top,
                    'fvg_bottom': fvg_bottom,
                    'fvg_midpoint': (fvg_top + fvg_bottom) / 2,
                    'formation_candles_after_raid': i
                })
    
    return valid_fvgs



def analyze_htf_liquidity_smc(htf_data: pd.DataFrame, lookback: int = 50, liquidity_threshold: float = 0.001) -> tuple:
    """
    MODIFIED: More sensitive liquidity detection using smart-money-concepts library
    """
    # Ensure we have OHLC columns with correct case
    ohlc = htf_data[['open', 'high', 'low', 'close']].copy()
    
    # Step 1: Detect swing highs and lows using MORE SENSITIVE parameters
    swing_highs_lows = smc.swing_highs_lows(ohlc, swing_length=3)  # CHANGED: 5 -> 3 (more sensitive)
    
    # Step 2: Detect liquidity zones with LARGER range
    liquidity_data = smc.liquidity(ohlc, swing_highs_lows, range_percent=0.02)  # CHANGED: 0.01 -> 0.02 (wider range)
    
    # Step 3: Detect liquidity raids (sweeps) with RELAXED threshold
    raids = []
    
    # Process each candle to check for sweeps
    for i in range(lookback, len(ohlc)):
        # Get current candle
        candle = ohlc.iloc[i]
        
        # Look back for valid liquidity levels
        lookback_start = max(0, i - lookback)
        
        # Check liquidity levels that haven't been swept
        for liq_idx in range(lookback_start, i):
            if liq_idx >= len(liquidity_data):
                continue
                
            liq_row = liquidity_data.iloc[liq_idx]
            
            # Skip if no liquidity or already swept
            if pd.isna(liq_row['Liquidity']) or liq_row.get('Swept', 0) != 0:
                continue
            
            liq_level = liq_row['Level']
            liq_type = liq_row['Liquidity']  # 1 for highs, -1 for lows
            
            # MODIFIED: More lenient sweep detection
            # Bearish raid: sweep above resistance liquidity
            if liq_type == 1:  # Resistance liquidity
                # CHANGED: Reduced threshold for easier detection
                if candle['high'] > liq_level * (1 + liquidity_threshold * 0.5):  # Halved threshold
                    # RELAXED: Don't require close below, just wick above
                    if candle['close'] <= liq_level * 1.002:  # Allow small close above
                        raids.append({
                            'raid_time': ohlc.index[i],
                            'raid_idx': i,
                            'raid_type': 'bearish',
                            'sweep_level': liq_level,
                            'raid_high': candle['high'],
                            'raid_close': candle['close'],
                            'liquidity_idx': liq_idx,
                            'setup_direction': 'short'
                        })
                        # Mark as swept
                        liquidity_data.loc[liquidity_data.index[liq_idx], 'Swept'] = 1
                        break  # Only one sweep per candle
            
            # Bullish raid: sweep below support liquidity  
            elif liq_type == -1:  # Support liquidity
                # CHANGED: Reduced threshold for easier detection
                if candle['low'] < liq_level * (1 - liquidity_threshold * 0.5):  # Halved threshold
                    # RELAXED: Don't require close above, just wick below
                    if candle['close'] >= liq_level * 0.998:  # Allow small close below
                        raids.append({
                            'raid_time': ohlc.index[i],
                            'raid_idx': i,
                            'raid_type': 'bullish',
                            'sweep_level': liq_level,
                            'raid_low': candle['low'],
                            'raid_close': candle['close'],
                            'liquidity_idx': liq_idx,
                            'setup_direction': 'long'
                        })
                        # Mark as swept
                        liquidity_data.loc[liquidity_data.index[liq_idx], 'Swept'] = 1
                        break  # Only one sweep per candle
    
    raids_df = pd.DataFrame(raids) if raids else pd.DataFrame()
    return raids_df, liquidity_data, swing_highs_lows


def find_ltf_fvg_smc(ltf_data: pd.DataFrame, raid_time: pd.Timestamp, 
                     raid_type: str, fvg_window: int = 10) -> List[Dict]:
    """
    MODIFIED: More sensitive FVG detection after HTF liquidity raid
    """
    # Find the index of raid time in LTF data
    raid_ltf_idx = None
    for i, time in enumerate(ltf_data.index):
        if time >= raid_time:
            raid_ltf_idx = i
            break
    
    if raid_ltf_idx is None or raid_ltf_idx >= len(ltf_data) - fvg_window:
        return []
    
    # MODIFIED: Look for FVGs in LARGER window after raid
    search_start = raid_ltf_idx
    search_end = min(raid_ltf_idx + fvg_window * 2, len(ltf_data))  # CHANGED: Doubled window size
    
    # Get window data
    window_data = ltf_data.iloc[search_start:search_end]
    if len(window_data) < 3:
        return []
    
    # Detect FVGs using SMC
    fvg_data = smc.fvg(window_data[['open', 'high', 'low', 'close']])
    
    # MODIFIED: More lenient FVG filtering
    valid_fvgs = []
    
    for i in range(len(fvg_data)):
        if pd.notna(fvg_data['FVG'].iloc[i]):
            fvg_value = fvg_data['FVG'].iloc[i]
            
            # RELAXED: Accept ANY FVG direction after raid (not just matching)
            # This allows for more complex market behavior
            if fvg_value != 0:  # Any FVG is valid
                
                # Get FVG boundaries
                fvg_top = fvg_data['Top'].iloc[i]
                fvg_bottom = fvg_data['Bottom'].iloc[i]
                
                # Calculate actual index in original data
                actual_idx = search_start + i
                
                valid_fvgs.append({
                    'fvg_time': window_data.index[i],
                    'fvg_idx': actual_idx,
                    'fvg_type': 'bearish' if fvg_value == -1 else 'bullish',
                    'fvg_top': fvg_top,
                    'fvg_bottom': fvg_bottom,
                    'fvg_midpoint': (fvg_top + fvg_bottom) / 2,
                    'formation_candles_after_raid': i
                })
    
    return valid_fvgs




def generate_raid_fvg_smc(data: pd.DataFrame, htf_analysis: Dict) -> List[ICTSignal]:
    """
    Generate RAID_FVG signals using smart-money-concepts library
    
    Complete rewrite using SMC library functions
    """
    signals = []
    
    print(f"\n🎯 Generating RAID_FVG signals using SMC library...")
    
    # Create HTF data
    htf_data = resample_data(data, config['htf_timeframe'])
    print(f"   📊 Created HTF data ({config['htf_timeframe']}) with {len(htf_data)} candles")
    
    # Analyze HTF liquidity raids
    htf_raids_df, liquidity_data, swing_data = analyze_htf_liquidity_smc(
        htf_data, 
        lookback=config.get('htf_lookback_candles', 100),
        liquidity_threshold=config.get('liquidity_buffer_pct', 0.15) / 100
    )
    
    print(f"   🔍 Found {len(htf_raids_df)} HTF liquidity raids")
    
    # Process each raid
    for _, raid in htf_raids_df.iterrows():
        raid_time = raid['raid_time']
        raid_type = raid['raid_type']
        sweep_level = raid['sweep_level']
        setup_direction = raid['setup_direction']
        
        print(f"\n   🎯 Processing HTF {raid_type} raid at {raid_time}")
        print(f"      • Sweep Level: ${sweep_level:.0f}")
        print(f"      • Setup Direction: {setup_direction.upper()}")
        
        # Find LTF FVGs after raid
        ltf_fvgs = find_ltf_fvg_smc(
            data, 
            raid_time, 
            raid_type,
            fvg_window=config.get('max_bars_after_raid', 30)
        )
        
        if not ltf_fvgs:
            print(f"      ❌ No valid LTF FVGs found after raid")
            continue
        
        # Use the first valid FVG
        best_fvg = ltf_fvgs[0]
        
        print(f"      ✅ Found {raid_type} FVG at {best_fvg['fvg_time']}")
        
        # Wait for retracement into FVG
        fvg_idx = best_fvg['fvg_idx']
        fvg_top = best_fvg['fvg_top']
        fvg_bottom = best_fvg['fvg_bottom']
        fvg_midpoint = best_fvg['fvg_midpoint']
        
        # Look for entry in next 100 candles
        entry_found = False
        entry_idx = None
        entry_price = None
        entry_time = None
        
        for i in range(fvg_idx + 1, min(fvg_idx + 100, len(data))):
            candle = data.iloc[i]
            
            # Check if price entered FVG zone
            if candle['low'] <= fvg_top and candle['high'] >= fvg_bottom:
                entry_found = True
                entry_idx = i
                entry_time = data.index[i]
                
                # Entry at FVG midpoint
                entry_price = fvg_midpoint
                
                # Adjust to actual executable price
                if setup_direction == 'long':
                    entry_price = max(min(entry_price, candle['high']), candle['low'])
                else:
                    entry_price = min(max(entry_price, candle['low']), candle['high'])
                
                break
        
        if not entry_found:
            print(f"      ❌ Price did not retrace to FVG")
            continue
        
        # Calculate HTF bias at signal time
        signal_time_data = data[data.index <= entry_time]
        signal_htf_analysis = analyze_htf_bias_and_liquidity(
            signal_time_data,
            config['htf_timeframe'],
            signal_time=entry_time
        )
        htf_bias = signal_htf_analysis.get('htf_bias', 'neutral')
        
        # Direction and risk management
        direction = 'bullish' if setup_direction == 'long' else 'bearish'
        
        # Calculate stop loss
        stop_loss = calculate_stop_loss_CORRECTED(
            entry_price, direction, fvg_top, fvg_bottom, data, entry_idx
        )
        
        # Calculate position size and take profit
        risk_amount = config['starting_balance'] * (config.get('risk_per_trade', 0.45) / 100)
        
        take_profit = calculate_take_profit_CORRECTED(
            entry_price, stop_loss, direction, risk_amount,
            fvg_top, fvg_bottom, data, entry_idx
        )
        
        # Validate R:R ratio
        if direction == 'bullish':
            rr_ratio = (take_profit - entry_price) / (entry_price - stop_loss) if entry_price > stop_loss else 0
        else:
            rr_ratio = (entry_price - take_profit) / (stop_loss - entry_price) if stop_loss > entry_price else 0
        
        if rr_ratio < config.get('min_rr_ratio', 1.5):
            print(f"      ❌ R:R ratio too low: {rr_ratio:.2f}")
            continue
        
        # Create signal
        signal = ICTSignal(
            signal_type='raid_fvg',
            direction=direction,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timeframe=config['ltf_timeframe'],
            confidence=0.85,
            timestamp=entry_time,
            setup_details={
                'raid_type': raid_type,
                'sweep_level': sweep_level,
                'raid_time': raid_time,
                'raid_idx': raid['raid_idx'],
                'liquidity_side': 'buy_side' if raid_type == 'bearish' else 'sell_side',
                'fvg_type': best_fvg['fvg_type'],
                'fvg_top': fvg_top,
                'fvg_bottom': fvg_bottom,
                'fvg_midpoint': fvg_midpoint,
                'fvg_time': best_fvg['fvg_time'],
                'fvg_idx': data.index[fvg_idx],
                'entry_idx': entry_idx,
                'htf_bias': htf_bias,
                'htf_bias_aligned': True,
                'rr_ratio': rr_ratio,
                'smc_library': True,  # Flag to indicate SMC library was used
                'signal_generation_time': entry_time,
                'htf_bias_calculation_time': entry_time
            }
        )
        
        signals.append(signal)
        print(f"      ✅ Generated {direction.upper()} signal using SMC library")
    
    print(f"\n   ✅ Generated {len(signals)} RAID_FVG signals using SMC library")
    return signals

# ==================== HTF BIAS AND LIQUIDITY ANALYSIS ====================
# VERSION 3: NO TRADES ON NEUTRAL - Filters out neutral completely

def analyze_htf_bias_and_liquidity(ltf_data: pd.DataFrame, htf_timeframe: str = '15m', signal_time: pd.Timestamp = None) -> Dict:
    """
    FIXED: Better debugging and data windowing for HTF bias
    """
    
    # Resample to HTF
    htf_data = resample_data(ltf_data, htf_timeframe)
    
    # Find signal position with better debugging
    if signal_time is not None:
        print(f"      📊 Analyzing HTF bias for signal time: {signal_time}")
        
        # Get data up to signal time (no lookahead)
        htf_data_no_lookahead = htf_data[htf_data.index <= signal_time]
        
        if len(htf_data_no_lookahead) < 20:
            print(f"      ⚠️ Insufficient HTF data: {len(htf_data_no_lookahead)} bars")
            return {
                'htf_bias': 'neutral',
                'htf_trend': 'neutral',
                'analysis_method': 'insufficient_data'
            }
        
        lookback_candles = min(config.get('htf_lookback_candles', 80), len(htf_data_no_lookahead))
        htf_window = htf_data_no_lookahead.tail(lookback_candles)
        
        print(f"      📊 Using {len(htf_window)} HTF bars ending at {htf_window.index[-1]}")
        print(f"      📊 HTF window: {htf_window.index[0]} to {htf_window.index[-1]}")
        
        # Show price context
        window_high = htf_window['high'].max()
        window_low = htf_window['low'].min()
        current_close = htf_window['close'].iloc[-1]
        range_position = (current_close - window_low) / (window_high - window_low) * 100
        
        print(f"      📊 Price context: High=${window_high:.0f}, Low=${window_low:.0f}, Close=${current_close:.0f}")
        print(f"      📊 Range position: {range_position:.1f}% (0%=bottom, 100%=top)")
        
    else:
        lookback_candles = config.get('htf_lookback_candles', 80)
        htf_window = htf_data.tail(lookback_candles)
    
    if len(htf_window) < 10:
        return {
            'htf_bias': 'neutral',
            'htf_trend': 'neutral',
            'analysis_method': 'insufficient_data'
        }
    
    # Calculate bias with better debugging
    htf_bias = analyze_simple_structure_bias(htf_window)
    print(f"      📊 FINAL HTF BIAS: {htf_bias}")
    
    # Rest of the function remains the same
    htf_structure = identify_market_structure(htf_window, lookback=10)
    htf_swings = []
    swing_highs = htf_structure[htf_structure['swing_high'].notna()]
    swing_lows = htf_structure[htf_structure['swing_low'].notna()]
    
    for idx, row in swing_highs.tail(3).iterrows():
        htf_swings.append({
            'type': 'high',
            'level': row['swing_high'],
            'timestamp': idx,
            'strength': 1.0
        })
    
    for idx, row in swing_lows.tail(3).iterrows():
        htf_swings.append({
            'type': 'low',
            'level': row['swing_low'],
            'timestamp': idx,
            'strength': 1.0
        })
    
    liquidity_zones = []
    if htf_swings:
        for swing in htf_swings[:3]:
            liquidity_zones.append({
                'level': swing['level'],
                'type': 'resistance' if swing['type'] == 'high' else 'support',
                'count': 1,
                'high': swing['level'] * 1.001,
                'low': swing['level'] * 0.999
            })
    
    return {
        'htf_bias': htf_bias,
        'htf_trend': htf_bias,
        'structure_bias': htf_bias,
        'momentum_bias': htf_bias,
        'htf_swings': htf_swings,
        'liquidity_zones': liquidity_zones,
        'htf_data': htf_data,
        'htf_window': htf_window,
        'htf_structure': htf_structure,
        'analysis_time': signal_time if signal_time else htf_window.index[-1],
        'bias_strength': 1.0,
        'analysis_method': 'fixed_structure_bias_v2',
        'lookback_candles_used': len(htf_window)
    }


def analyze_simple_structure_bias(htf_window: pd.DataFrame) -> str:
    """
    COMPLETELY FIXED: More accurate HTF bias detection that properly identifies trends
    """
    if len(htf_window) < 10:
        return 'neutral'
    
    current_close = htf_window['close'].iloc[-1]
    
    # 1. PRICE MOMENTUM (Most Important) - Fixed thresholds
    momentum_bias = 'neutral'
    
    # Look at different timeframes for momentum
    momentum_scores = {'bullish': 0, 'bearish': 0}
    
    # Short-term momentum (last 3 bars)
    if len(htf_window) >= 3:
        close_3_ago = htf_window['close'].iloc[-3]
        momentum_3 = (current_close - close_3_ago) / close_3_ago * 100
        
        if momentum_3 > 1.0:  # 1% up in 3 bars
            momentum_scores['bullish'] += 2
        elif momentum_3 < -1.0:  # 1% down in 3 bars
            momentum_scores['bearish'] += 2
    
    # Medium-term momentum (last 7 bars)
    if len(htf_window) >= 7:
        close_7_ago = htf_window['close'].iloc[-7]
        momentum_7 = (current_close - close_7_ago) / close_7_ago * 100
        
        if momentum_7 > 2.0:  # 2% up in 7 bars
            momentum_scores['bullish'] += 3
        elif momentum_7 < -2.0:  # 2% down in 7 bars
            momentum_scores['bearish'] += 3
        elif momentum_7 > 0.5:
            momentum_scores['bullish'] += 1
        elif momentum_7 < -0.5:
            momentum_scores['bearish'] += 1
    
    # Long-term momentum (last 15 bars)
    if len(htf_window) >= 15:
        close_15_ago = htf_window['close'].iloc[-15]
        momentum_15 = (current_close - close_15_ago) / close_15_ago * 100
        
        if momentum_15 > 3.0:  # 3% up in 15 bars
            momentum_scores['bullish'] += 4
        elif momentum_15 < -3.0:  # 3% down in 15 bars
            momentum_scores['bearish'] += 4
        elif momentum_15 > 1.0:
            momentum_scores['bullish'] += 2
        elif momentum_15 < -1.0:
            momentum_scores['bearish'] += 2
    
    # 2. MOVING AVERAGE ANALYSIS (Fixed)
    ma_bias = 'neutral'
    ma_scores = {'bullish': 0, 'bearish': 0}
    
    if len(htf_window) >= 10:
        ma10 = htf_window['close'].tail(10).mean()
        
        # Price vs MA10
        ma_diff_pct = (current_close - ma10) / ma10 * 100
        
        if ma_diff_pct > 1.0:  # 1% above MA
            ma_scores['bullish'] += 2
        elif ma_diff_pct < -1.0:  # 1% below MA
            ma_scores['bearish'] += 2
        elif ma_diff_pct > 0.2:
            ma_scores['bullish'] += 1
        elif ma_diff_pct < -0.2:
            ma_scores['bearish'] += 1
        
        # MA slope (trend direction)
        if len(htf_window) >= 15:
            ma10_now = ma10
            ma10_5_ago = htf_window['close'].iloc[-15:-5].mean()
            
            ma_slope_pct = (ma10_now - ma10_5_ago) / ma10_5_ago * 100
            
            if ma_slope_pct > 0.5:
                ma_scores['bullish'] += 2
            elif ma_slope_pct < -0.5:
                ma_scores['bearish'] += 2
    
    # 3. CANDLE PATTERN ANALYSIS (Fixed)
    candle_scores = {'bullish': 0, 'bearish': 0}
    
    # Last 5 candles
    if len(htf_window) >= 5:
        recent_candles = htf_window.tail(5)
        
        bullish_candles = 0
        bearish_candles = 0
        
        for _, candle in recent_candles.iterrows():
            if candle['close'] > candle['open']:
                bullish_candles += 1
            elif candle['close'] < candle['open']:
                bearish_candles += 1
        
        if bullish_candles >= 4:
            candle_scores['bullish'] += 3
        elif bullish_candles >= 3:
            candle_scores['bullish'] += 1
        
        if bearish_candles >= 4:
            candle_scores['bearish'] += 3
        elif bearish_candles >= 3:
            candle_scores['bearish'] += 1
    
    # 4. HIGH/LOW PROGRESSION (Fixed)
    hl_scores = {'bullish': 0, 'bearish': 0}
    
    if len(htf_window) >= 10:
        recent_data = htf_window.tail(10)
        
        # Compare recent highs and lows
        first_half_high = recent_data.iloc[:5]['high'].max()
        second_half_high = recent_data.iloc[5:]['high'].max()
        
        first_half_low = recent_data.iloc[:5]['low'].min()
        second_half_low = recent_data.iloc[5:]['low'].min()
        
        # Higher highs and higher lows = bullish
        if second_half_high > first_half_high and second_half_low > first_half_low:
            hl_scores['bullish'] += 3
        # Lower highs and lower lows = bearish
        elif second_half_high < first_half_high and second_half_low < first_half_low:
            hl_scores['bearish'] += 3
        # Mixed signals
        elif second_half_high > first_half_high:
            hl_scores['bullish'] += 1
        elif second_half_high < first_half_high:
            hl_scores['bearish'] += 1
    
    # 5. RANGE POSITION (Fixed)
    range_scores = {'bullish': 0, 'bearish': 0}
    
    if len(htf_window) >= 20:
        range_data = htf_window.tail(20)
        high_20 = range_data['high'].max()
        low_20 = range_data['low'].min()
        
        if high_20 > low_20:
            range_position = (current_close - low_20) / (high_20 - low_20)
            
            if range_position > 0.75:  # In upper 25% of range
                range_scores['bullish'] += 2
            elif range_position > 0.60:  # In upper 40% of range
                range_scores['bullish'] += 1
            elif range_position < 0.25:  # In lower 25% of range
                range_scores['bearish'] += 2
            elif range_position < 0.40:  # In lower 40% of range
                range_scores['bearish'] += 1
    
    # COMBINE ALL SCORES WITH PROPER WEIGHTING
    total_bullish = (
        momentum_scores['bullish'] * 3 +  # Momentum most important
        ma_scores['bullish'] * 2 +        # MA trend second
        hl_scores['bullish'] * 2 +        # Structure important
        candle_scores['bullish'] * 1 +    # Candles less important
        range_scores['bullish'] * 1       # Range position least important
    )
    
    total_bearish = (
        momentum_scores['bearish'] * 3 +
        ma_scores['bearish'] * 2 +
        hl_scores['bearish'] * 2 +
        candle_scores['bearish'] * 1 +
        range_scores['bearish'] * 1
    )
    
    # Debug information
    print(f"      HTF Bias Calculation Debug:")
    print(f"        Momentum: Bull={momentum_scores['bullish']}, Bear={momentum_scores['bearish']}")
    print(f"        MA: Bull={ma_scores['bullish']}, Bear={ma_scores['bearish']}")
    print(f"        HL: Bull={hl_scores['bullish']}, Bear={hl_scores['bearish']}")
    print(f"        Candles: Bull={candle_scores['bullish']}, Bear={candle_scores['bearish']}")
    print(f"        Range: Bull={range_scores['bullish']}, Bear={range_scores['bearish']}")
    print(f"        TOTAL: Bull={total_bullish}, Bear={total_bearish}")
    
    # FINAL DETERMINATION (Fixed thresholds)
    if total_bearish > total_bullish + 3:  # Clear bearish bias
        return 'bearish'
    elif total_bullish > total_bearish + 3:  # Clear bullish bias
        return 'bullish'
    elif total_bearish > total_bullish + 1:  # Lean bearish
        return 'lean_bearish'
    elif total_bullish > total_bearish + 1:  # Lean bullish
        return 'lean_bullish'
    else:
        return 'neutral'

        
       

# ==================== TRADE EXECUTION WITH DEBUG ====================
def execute_trades_debug_improved(signals: List[ICTSignal], data: pd.DataFrame, m1_data: pd.DataFrame) -> List[Dict]:
    """
    RESTORED: Back to original working entry logic that was working correctly
    """
    trades = []
    balance = config['starting_balance']
    
    # Remove duplicate signals first
    unique_signals = []
    seen_signals = set()
    
    for signal in signals:
        signal_key = (
            signal.timestamp,
            round(signal.entry_price, 2),
            signal.direction,
            signal.signal_type
        )
        
        if signal_key not in seen_signals:
            seen_signals.add(signal_key)
            unique_signals.append(signal)
    
    signals = sorted(unique_signals, key=lambda x: x.timestamp)
    
    print(f"\n🔍 Trade Execution:")
    print(f"   Signals to process: {len(signals)}")
    
    risk_mode = config.get('risk_management_mode', 'compounded')
    baseline_risk_amount = config['starting_balance'] * (config.get('risk_per_trade', 0.45) / 100)
    
    rejection_reasons = {
        'active_trade': 0,
        'daily_limit': 0,
        'entry_not_touched': 0,
        'fee_too_high': 0,
        'no_exit': 0,
        'index_error': 0,
        'stop_too_close': 0,
        'rr_too_low': 0
    }
    
    daily_trades = {}
    executed_trades = []
    
    for idx, signal in enumerate(signals):
        
        # Check for active trades
        has_active_trade = False
        for prev_trade in executed_trades:
            if prev_trade['entry_time'] <= signal.timestamp <= prev_trade['exit_time']:
                has_active_trade = True
                break
        
        if has_active_trade:
            rejection_reasons['active_trade'] += 1
            continue
        
        # Check daily limit
        trade_date = signal.timestamp.date()
        if trade_date not in daily_trades:
            daily_trades[trade_date] = 0
            
        if daily_trades[trade_date] >= config.get('max_trades_per_day', 10):
            rejection_reasons['daily_limit'] += 1
            continue
        
        # Find signal position in 1-minute data
        signal_m1_idx = None
        for i, m1_time in enumerate(m1_data.index):
            if m1_time >= signal.timestamp:
                signal_m1_idx = i
                break
        
        if signal_m1_idx is None:
            rejection_reasons['index_error'] += 1
            continue
        
        # ORIGINAL ENTRY LOGIC - Look for entry touch within reasonable time
        entry_found = False
        actual_entry_price = signal.entry_price
        actual_entry_time = signal.timestamp
        entry_m1_idx = signal_m1_idx
        
        # Look for entry within next 20 bars (20 minutes)
        max_wait_bars = 20
        entry_tolerance = signal.entry_price * 0.001  # 0.1% tolerance
        
        for i in range(signal_m1_idx, min(signal_m1_idx + max_wait_bars, len(m1_data))):
            m1_candle = m1_data.iloc[i]
            
            # Check if price touched the entry level
            if (m1_candle['low'] <= signal.entry_price + entry_tolerance and 
                m1_candle['high'] >= signal.entry_price - entry_tolerance):
                
                # Price touched entry level
                entry_found = True
                actual_entry_time = m1_data.index[i]
                entry_m1_idx = i
                
                # Adjust entry price based on direction and candle
                if signal.direction == 'bullish':
                    actual_entry_price = max(signal.entry_price, m1_candle['low'])
                else:
                    actual_entry_price = min(signal.entry_price, m1_candle['high'])
                
                break
        
        if not entry_found:
            rejection_reasons['entry_not_touched'] += 1
            continue
        
        # Calculate risk amount
        if risk_mode == 'fixed':
            actual_risk_for_this_trade = baseline_risk_amount
        else:
            actual_risk_for_this_trade = balance * (config.get('risk_per_trade', 0.45) / 100)
        
        # Use signal's original stop loss and take profit
        actual_stop_loss = signal.stop_loss
        actual_take_profit = signal.take_profit
        
        # Validate stop distance
        stop_distance = abs(actual_entry_price - actual_stop_loss)
        stop_distance_pct = stop_distance / actual_entry_price * 100
        
        if stop_distance_pct < config.get('min_stop_distance_pct', 0.1):
            rejection_reasons['stop_too_close'] += 1
            continue
        
        # Position size calculation
        position_size_btc, adjusted_risk, estimated_fees = calculate_position_size_with_fee_constraint(
            actual_entry_price,
            actual_stop_loss,
            actual_risk_for_this_trade,
            signal.direction
        )

        if adjusted_risk != actual_risk_for_this_trade:
            actual_risk_for_this_trade = adjusted_risk
        
        # Fee validation
        entry_value_usd = actual_entry_price * position_size_btc
        tp_value_usd = actual_take_profit * position_size_btc
        
        entry_fee = entry_value_usd * config.get('fee_rate', 0.0006)
        tp_exit_fee = tp_value_usd * config.get('fee_rate', 0.0006)
        total_estimated_fees = entry_fee + tp_exit_fee
        
        fee_pct_of_risk = (total_estimated_fees / actual_risk_for_this_trade) * 100
        
        if fee_pct_of_risk > config.get('max_fee_pct_of_risk', 60.0):
            rejection_reasons['fee_too_high'] += 1
            continue
        
        # R:R validation
        if signal.direction == 'bullish':
            actual_rr = (actual_take_profit - actual_entry_price) / (actual_entry_price - actual_stop_loss)
        else:
            actual_rr = (actual_entry_price - actual_take_profit) / (actual_stop_loss - actual_entry_price)
        
        if actual_rr < config.get('min_rr_ratio', 1.5):
            rejection_reasons['rr_too_low'] += 1
            continue
        
        # Find exit
        exit_m1_idx = None
        exit_price = None
        hit_stop = False
        
        for i in range(entry_m1_idx + 1, min(entry_m1_idx + 2000, len(m1_data))):
            m1_candle = m1_data.iloc[i]
            
            if signal.direction == 'bullish':
                if m1_candle['low'] <= actual_stop_loss:
                    exit_m1_idx = i
                    exit_price = actual_stop_loss
                    hit_stop = True
                    break
                elif m1_candle['high'] >= actual_take_profit:
                    exit_m1_idx = i
                    exit_price = actual_take_profit
                    break
            else:
                if m1_candle['high'] >= actual_stop_loss:
                    exit_m1_idx = i
                    exit_price = actual_stop_loss
                    hit_stop = True
                    break
                elif m1_candle['low'] <= actual_take_profit:
                    exit_m1_idx = i
                    exit_price = actual_take_profit
                    break
        
        if exit_m1_idx is None:
            rejection_reasons['no_exit'] += 1
            continue
        
        # Calculate P&L
        entry_value_usd = actual_entry_price * position_size_btc
        exit_value_usd = exit_price * position_size_btc
        
        if signal.direction == 'bullish':
            gross_pnl = exit_value_usd - entry_value_usd
        else:
            gross_pnl = entry_value_usd - exit_value_usd
        
        actual_entry_fee = entry_value_usd * config.get('fee_rate', 0.0006)
        actual_exit_fee = exit_value_usd * config.get('fee_rate', 0.0006)
        total_actual_fees = actual_entry_fee + actual_exit_fee
        
        net_pnl = gross_pnl - total_actual_fees
        
        if risk_mode == 'fixed':
            r_outcome = net_pnl / baseline_risk_amount
        else:
            r_outcome = net_pnl / actual_risk_for_this_trade
        
        balance += net_pnl
        
        # Create trade record
        trade = {
            'trade_id': f"{signal.signal_type}_{actual_entry_time.strftime('%Y%m%d_%H%M')}_{signal.direction}",
            'entry_time': actual_entry_time,
            'exit_time': m1_data.index[exit_m1_idx],
            'direction': signal.direction,
            'signal_type': signal.signal_type,
            'signal_time': signal.timestamp,
            'entry_price': actual_entry_price,
            'planned_entry': signal.entry_price,
            'exit_price': exit_price,
            'stop_loss': actual_stop_loss,
            'take_profit': actual_take_profit,
            'position_size_btc': position_size_btc,
            'position_value_usd': entry_value_usd,
            'stop_distance_usd': stop_distance,
            'risk_amount': actual_risk_for_this_trade,
            'gross_pnl': gross_pnl,
            'total_fees': total_actual_fees,
            'net_pnl': net_pnl,
            'r_outcome': r_outcome,
            'actual_rr': actual_rr,
            'hit_stop': hit_stop,
            'balance': balance,
            'confidence': signal.confidence,
            'setup_details': signal.setup_details,
            'entry_idx': entry_m1_idx,
            'exit_idx': exit_m1_idx,
            'fee_pct_of_risk': fee_pct_of_risk,
            'risk_mode': risk_mode,
            'wait_time_minutes': (actual_entry_time - signal.timestamp).total_seconds() / 60
        }
        
        trades.append(trade)
        daily_trades[trade_date] += 1
        
        executed_trades.append({
            'entry_time': actual_entry_time,
            'exit_time': m1_data.index[exit_m1_idx],
            'trade_id': trade['trade_id']
        })
        
        if config.get('debug_mode') and len(trades) <= 10:
            print(f"   ✅ Trade {len(trades)}: {signal.signal_type} at {actual_entry_time.strftime('%m-%d %H:%M')}")
    
    print(f"\n📊 Execution Summary:")
    for reason, count in rejection_reasons.items():
        if count > 0:
            print(f"   {reason}: {count}")
    
    print(f"   Total executed: {len(trades)}/{len(signals)}")
    
    return trades




# ==================== ENHANCED CHART PLOTTING ====================

def plot_enhanced_ict_chart_FIXED(trade: Dict, data: pd.DataFrame, trade_num: int):
    """ENHANCED: Now shows HTF FVG zones, liquidity raids, and FVG zones properly"""
    if not config['plot_charts']:
        return
        
    try:
        # Convert 1-minute indices to 5-minute indices for plotting
        entry_time = trade['entry_time']
        exit_time = trade['exit_time']
        
        # Find corresponding indices
        entry_idx = None
        exit_idx = None
        
        for i, time in enumerate(data.index):
            if entry_idx is None and time >= entry_time:
                entry_idx = i 
            if exit_idx is None and time >= exit_time:
                exit_idx = i
                break
        
        if entry_idx is None:
            entry_idx = 0
        if exit_idx is None:
            exit_idx = len(data) - 1
            
        setup_details = trade.get('setup_details', {})
        
        # Create window - back to original
        start_idx = max(0, entry_idx - 100)
        end_idx = min(len(data), exit_idx + 30)
        plot_data = data.iloc[start_idx:end_idx]
        
        # Create figure
        fig, ax = plt.subplots(figsize=(20, 12))
        
        # Plot candlesticks
        for i, (time, candle) in enumerate(plot_data.iterrows()):
            color = 'green' if candle['close'] > candle['open'] else 'red'
            
            # Body
            body_height = abs(candle['close'] - candle['open'])
            body_bottom = min(candle['open'], candle['close'])
            ax.add_patch(Rectangle((i-0.3, body_bottom), 0.6, body_height,
                                  facecolor=color, edgecolor='black', alpha=0.8))
            
            # Wicks
            ax.plot([i, i], [candle['low'], candle['high']], color='black', linewidth=1)
        
        # FIXED: Plot Liquidity Raid Level and Mark
        sweep_level = setup_details.get('sweep_level', 0)
        raid_time = setup_details.get('raid_time')
        
        if sweep_level > 0 and raid_time:
            # Find raid position in plot data
            raid_plot_x = None
            for i, time in enumerate(plot_data.index):
                if time >= raid_time:
                    raid_plot_x = i
                    break
            
            if raid_plot_x is not None:
                # Draw liquidity level line up to raid point
                ax.plot([0, raid_plot_x], [sweep_level, sweep_level], 
                       color='purple', linestyle='--', linewidth=2.5, alpha=0.8, 
                       label=f'Liquidity Level: ${sweep_level:.0f}')
                
                # Mark the actual raid point
                ax.scatter(raid_plot_x, sweep_level, color='purple', marker='X', 
                          s=200, zorder=10, label='Liquidity Raid')
                
                print(f"   📊 Plotted raid at x={raid_plot_x}, level=${sweep_level:.0f}")
        
        # FIXED: Plot LTF FVG Zone properly
        fvg_time = setup_details.get('fvg_time')
        fvg_top = setup_details.get('fvg_top', 0)
        fvg_bottom = setup_details.get('fvg_bottom', 0)
        fvg_type = setup_details.get('fvg_type', 'unknown')
        
        if fvg_top > fvg_bottom and fvg_time:
            # Find FVG position in plot data
            fvg_plot_x = None
            for i, time in enumerate(plot_data.index):
                if time >= fvg_time:
                    fvg_plot_x = i
                    break
            
            if fvg_plot_x is not None:
                fvg_color = 'green' if fvg_type == 'bullish' else 'red'
                
                # Draw FVG rectangle
                fvg_width = min(20, len(plot_data) - fvg_plot_x)
                ax.add_patch(Rectangle((fvg_plot_x - 1, fvg_bottom), fvg_width, fvg_top - fvg_bottom,
                                     facecolor=fvg_color, alpha=0.3, 
                                     edgecolor=fvg_color, linewidth=2, 
                                     label=f'LTF {fvg_type.title()} FVG'))
                
                print(f"   📊 Plotted FVG at x={fvg_plot_x}, top=${fvg_top:.0f}, bottom=${fvg_bottom:.0f}")
        
        # HTF FVG zone (if available)
        htf_fvg_high = setup_details.get('htf_fvg_high', 0)
        htf_fvg_low = setup_details.get('htf_fvg_low', 0)
        htf_fvg_type = setup_details.get('htf_fvg_type', None)
        
        if htf_fvg_high > htf_fvg_low and htf_fvg_type:
            htf_fvg_color = 'green' if htf_fvg_type == 'bullish' else 'red'
            
            ax.add_patch(Rectangle((0, htf_fvg_low), len(plot_data), htf_fvg_high - htf_fvg_low,
                                 facecolor=htf_fvg_color, alpha=0.15, 
                                 edgecolor=htf_fvg_color, linewidth=2, linestyle='--',
                                 label=f'HTF {htf_fvg_type.title()} FVG Zone'))
        
        # Entry and exit
        entry_x = entry_idx - start_idx
        exit_x = exit_idx - start_idx

        ax.axvline(x=entry_x, color='orange', linestyle='--', 
                linewidth=1.5, alpha=0.6, zorder=3)

        entry_time_str = trade['entry_time'].strftime('%m/%d %H:%M')
        price_low = plot_data['low'].min()
        price_range = plot_data['high'].max() - price_low
        annotation_y = price_low + (price_range * 0.05)

        ax.text(entry_x + 2, annotation_y, f'Entry: {entry_time_str}', 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7),
                verticalalignment='bottom', horizontalalignment='left',
                fontsize=9, color='white', fontweight='bold')

        # Entry
        ax.axhline(y=trade['entry_price'], color='blue', linestyle='-', 
                  linewidth=2, alpha=0.8, label='Entry Price')
        ax.scatter(entry_x, trade['entry_price'], color='blue', 
                  marker='o', s=100, zorder=5, label='Entry')
        
        # Exit
        exit_color = 'red' if trade['hit_stop'] else 'green'
        exit_label = 'Stop Loss Hit' if trade['hit_stop'] else 'Take Profit Hit'
        ax.scatter(exit_x, trade['exit_price'], color=exit_color, 
                  marker='X', s=150, zorder=5, label=exit_label)
        
        # Stop loss and take profit lines
        ax.axhline(y=trade['stop_loss'], color='red', linestyle='--', 
                  linewidth=1.5, alpha=0.7, label='Stop Loss')
        ax.axhline(y=trade['take_profit'], color='green', linestyle='--', 
                  linewidth=1.5, alpha=0.7, label='Take Profit')
        
        # Price range for positioning
        price_high = plot_data['high'].max()
        price_low = plot_data['low'].min()
        price_range = price_high - price_low
        buffer = price_range * 0.02
        
        chart_width = len(plot_data)
        label_x = chart_width + 3
        
        # Price labels
        min_label_spacing = price_range * 0.03
        
        ax.text(label_x, trade['entry_price'], 
               f"Entry: ${trade['entry_price']:.0f}", 
               verticalalignment='center', fontsize=10, fontweight='bold', color='white',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.9))
        
        sl_y = trade['stop_loss']
        if abs(sl_y - trade['entry_price']) < min_label_spacing:
            if sl_y < trade['entry_price']:
                sl_y = trade['entry_price'] - min_label_spacing
            else:
                sl_y = trade['entry_price'] + min_label_spacing
        
        ax.text(label_x, sl_y, 
               f"SL: ${trade['stop_loss']:.0f}", 
               verticalalignment='center', fontsize=10, fontweight='bold', color='white',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.9))
        
        tp_y = trade['take_profit']
        if abs(tp_y - trade['entry_price']) < min_label_spacing:
            if tp_y > trade['entry_price']:
                tp_y = trade['entry_price'] + min_label_spacing
            else:
                tp_y = trade['entry_price'] - min_label_spacing
        
        if abs(tp_y - sl_y) < min_label_spacing:
            if tp_y > sl_y:
                tp_y = sl_y + min_label_spacing
            else:
                tp_y = sl_y - min_label_spacing
        
        ax.text(label_x, tp_y, 
               f"TP: ${trade['take_profit']:.0f}", 
               verticalalignment='center', fontsize=10, fontweight='bold', color='white',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.9))
        
        # Stats text box
        htf_bias = setup_details.get('htf_bias', 'unknown')
        direction_text = 'LONG' if trade['direction'] == 'bullish' else 'SHORT'
        
        stats_text = f"Signal: {trade['signal_type'].upper()}\n"
        stats_text += f"Direction: {direction_text}\n"
        stats_text += f"HTF Bias: {htf_bias.upper()}\n"
        stats_text += f"Position: {trade['position_size_btc']:.4f} BTC\n"
        stats_text += f"R: {trade['r_outcome']:.2f}\n"
        stats_text += f"Risk: ${trade['risk_amount']:.0f}\n"
        stats_text += f"P&L: ${trade['net_pnl']:.2f}\n"
        stats_text += f"Fees: ${trade['total_fees']:.2f}"
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', 
                         edgecolor='black', alpha=0.9),
                verticalalignment='top', fontsize=10)
        
        # Legend
        ax.legend(loc='upper right', fontsize=9, 
                 bbox_to_anchor=(0.98, 0.98), framealpha=0.9, ncol=2)
        
        # Title
        direction = 'LONG' if trade['direction'] == 'bullish' else 'SHORT'
        title = f"Trade #{trade_num} - {direction} - {trade['signal_type'].upper()} (LTF)"
        ax.set_title(title, fontsize=16, pad=20)
        
        # Format axes
        ax.set_ylabel('Price ($)', fontsize=12)
        ax.set_xlabel('Time', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Proper axis limits
        ax.set_xlim(-2, chart_width + 15)
        ax.set_ylim(price_low - buffer * 4, price_high + buffer * 3)
        
        # X-axis labels - more labels for 200 candles
        date_labels = []
        date_positions = []
        step = max(1, len(plot_data) // 10)
        for i in range(0, len(plot_data), step):
            date_labels.append(plot_data.index[i].strftime('%m/%d %H:%M'))
            date_positions.append(i)
        
        ax.set_xticks(date_positions)
        ax.set_xticklabels(date_labels, rotation=45)
        
        plt.tight_layout()
        
        # Save chart
        if not os.path.exists('charts'):
            os.makedirs('charts')
        filename = f'charts/trade_{trade_num}_ltf.png'
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ Chart saved: {filename}")
        
        if config['plot_htf_charts']:
            plot_htf_chart(trade, data, trade_num, direction)
        
    except Exception as e:
        print(f"   ⚠️ Error plotting chart {trade_num}: {e}")
        import traceback
        traceback.print_exc()
     
     




# ==================== FIXED: HTF Bias Must Be Set Before Creating Signals ====================

def generate_all_signal_types_with_special_patterns(data: pd.DataFrame) -> List[ICTSignal]:
    """
    FIXED: Removed duplicate HTF analysis - now only done at signal generation time
    """
    
    print(f"\n🔍 Generating signals with FIXED HTF timing...")
    
    all_signals = []
    
    # Check if HTF analysis is needed
    use_htf_analysis = config.get('use_htf_bias', False)
    allow_neutral_trades = config.get('allow_neutral_trades', False)
    
    # Create HTF data early for strategies that need it
    htf_data = None
    if config.get('generate_displacement_fvg', True) or config.get('generate_raid_fvg', False):
        try:
            htf_data = resample_data(data, config['htf_timeframe'])
            print(f"   📊 Created HTF data ({config['htf_timeframe']}) for FVG strategies")
        except Exception as e:
            print(f"   ❌ Error creating HTF data: {e}")
            return []
    
    if use_htf_analysis:
        print(f"   📊 HTF analysis enabled (FIXED: bias calculated at signal generation time)")
        print(f"   🎯 Neutral trades: {'ALLOWED' if allow_neutral_trades else 'BLOCKED'}")
    else:
        print(f"   ⚡ HTF analysis disabled - ALL SIGNALS WILL BE KEPT")
    
    temp_signals = []
    
    # RAID_FVG STRATEGY  
    if config.get('generate_raid_fvg', False):
        print(f"   ⚡ Generating RAID_FVG signals...")
        
        empty_htf_analysis = {
            'htf_bias': 'neutral',
            'htf_trend': 'neutral',
            'htf_swings': [],
            'liquidity_zones': []
        }
        
        raid_fvg_signals = generate_raid_fvg_smc(data, empty_htf_analysis)
        temp_signals.extend(raid_fvg_signals)
        print(f"   ✅ Generated {len(raid_fvg_signals)} RAID_FVG signals")
    
    # FIXED: HTF bias filtering - ONLY run if HTF analysis is enabled
    if use_htf_analysis and temp_signals:
        print(f"\n   🔍 Applying HTF bias filtering (using signal-generation-time bias)...")
        
        signals_kept = 0
        signals_filtered_neutral = 0
        signals_filtered_counter = 0
        
        for i, signal in enumerate(temp_signals):
            try:
                # HTF bias is already calculated at signal generation time
                htf_bias = signal.setup_details.get('htf_bias', 'neutral')
                
                print(f"   📊 Signal {i+1}: HTF bias (signal time): {htf_bias}, Direction: {signal.direction}")
                
                # Filtering logic
                if htf_bias == 'neutral':
                    if allow_neutral_trades:
                        all_signals.append(signal)
                        signals_kept += 1
                        print(f"   ✅ Neutral bias allowed")
                    else:
                        signals_filtered_neutral += 1
                        print(f"   ❌ Neutral bias blocked")
                elif htf_bias == signal.direction or htf_bias == f'lean_{signal.direction}':
                    all_signals.append(signal)
                    signals_kept += 1
                    print(f"   ✅ HTF bias aligned with signal direction")
                else:
                    signals_filtered_counter += 1
                    print(f"   ❌ HTF bias counter to signal direction")
                    
            except Exception as e:
                print(f"   ⚠️ Error processing signal {i}: {e}")
                # Add signal anyway with neutral bias
                signal.setup_details['htf_bias'] = 'neutral'
                all_signals.append(signal)
                signals_kept += 1
        
        print(f"   📊 HTF Analysis Results:")
        print(f"       Signals kept: {signals_kept}")
        print(f"       Filtered (neutral): {signals_filtered_neutral}")
        print(f"       Filtered (counter-trend): {signals_filtered_counter}")
    
    else:
        # FIXED: No HTF analysis - add ALL signals with neutral HTF bias
        print(f"\n   ⚡ HTF analysis disabled - keeping all {len(temp_signals)} signals")
        for signal in temp_signals:
            try:
                if 'htf_bias' not in signal.setup_details:
                    signal.setup_details['htf_bias'] = 'neutral'
                all_signals.append(signal)
            except Exception as e:
                print(f"   ⚠️ Error adding signal: {e}")
                continue
    
    print(f"   ✅ Total signals generated: {len(all_signals)}")
    
    return all_signals



def plot_htf_chart(trade: Dict, ltf_data: pd.DataFrame, trade_num: int, direction: str):
    """ENHANCED: Now shows HTF FVG zones, PDL, and liquidity raid on HTF chart"""
    try:
        # Get HTF data
        htf_data = resample_data(ltf_data, config['htf_timeframe'])
        
        # Find trade position in HTF data
        entry_time = trade['entry_time']
        entry_htf_idx = None
        for i, htf_time in enumerate(htf_data.index):
            if htf_time >= entry_time:
                entry_htf_idx = i
                break
        
        if entry_htf_idx is None:
            print(f"   ⚠️ Could not find HTF entry for trade {trade_num}")
            return
        
        # Create window
        start_idx = max(0, entry_htf_idx - 200)
        end_idx = min(len(htf_data), entry_htf_idx + 100)
        plot_data = htf_data.iloc[start_idx:end_idx]
        
        # Calculate Previous Day Low (PDL) for HTF
        entry_date = entry_time.date()
        previous_day = entry_date - pd.Timedelta(days=1)
        
        # Find PDL from HTF data
        pdl_value = None
        for i in range(len(htf_data)):
            if htf_data.index[i].date() == previous_day:
                if pdl_value is None:
                    pdl_value = htf_data.iloc[i]['low']
                else:
                    pdl_value = min(pdl_value, htf_data.iloc[i]['low'])
            elif htf_data.index[i].date() < previous_day:
                break
        
        # Create figure
        fig, ax = plt.subplots(figsize=(20, 12))
        
        # Plot HTF candlesticks
        for i, (time, candle) in enumerate(plot_data.iterrows()):
            color = 'green' if candle['close'] > candle['open'] else 'red'
            body_height = abs(candle['close'] - candle['open'])
            body_bottom = min(candle['open'], candle['close'])
            ax.add_patch(Rectangle((i-0.3, body_bottom), 0.6, body_height,
                                  facecolor=color, edgecolor='black', alpha=0.8))
            ax.plot([i, i], [candle['low'], candle['high']], color='black', linewidth=1)
        
        # Plot Previous Day Low (PDL)
        if pdl_value:
            ax.axhline(y=pdl_value, color='red', linestyle='--', linewidth=2, alpha=0.7, label=f'PDL: ${pdl_value:.0f}')
            ax.text(len(plot_data) * 0.02, pdl_value, f'PDL', 
                   verticalalignment='bottom', fontsize=12, color='red', fontweight='bold')
        
        # FIXED: Plot Liquidity Raid Level using correct setup_details keys
        setup_details = trade.get('setup_details', {})
        sweep_level = setup_details.get('sweep_level', 0)
        raid_time = setup_details.get('raid_time')
        
        if sweep_level > 0 and raid_time:
            # Find raid position in HTF plot data
            raid_htf_x = None
            for i, htf_time in enumerate(plot_data.index):
                if htf_time >= raid_time:
                    raid_htf_x = i
                    break
            
            if raid_htf_x is not None:
                # Draw liquidity level line up to raid point
                ax.plot([0, raid_htf_x], [sweep_level, sweep_level], 
                       color='purple', linestyle='--', linewidth=2.5, alpha=0.8, 
                       label=f'Liquidity Level: ${sweep_level:.0f}')
                
                # Mark the raid point
                ax.scatter(raid_htf_x, sweep_level, color='purple', marker='X', 
                          s=200, zorder=10, label='Liquidity Raid')
        
        # HTF FVG zone
        htf_fvg_high = setup_details.get('htf_fvg_high', 0)
        htf_fvg_low = setup_details.get('htf_fvg_low', 0)
        htf_fvg_type = setup_details.get('htf_fvg_type', None)
        htf_fvg_formation_time = setup_details.get('htf_fvg_formation_time')
        
        if htf_fvg_high > htf_fvg_low and htf_fvg_type:
            htf_fvg_x = None
            if htf_fvg_formation_time:
                for i, time in enumerate(plot_data.index):
                    if time >= htf_fvg_formation_time:
                        htf_fvg_x = i
                        break
            
            if htf_fvg_x is None:
                htf_fvg_x = max(0, entry_htf_idx - start_idx - 20)
            
            htf_fvg_color = 'green' if htf_fvg_type == 'bullish' else 'red'
            
            fvg_start_x = max(0, htf_fvg_x - 1)
            fvg_width = min(30, len(plot_data) - fvg_start_x)
            
            ax.add_patch(Rectangle((fvg_start_x, htf_fvg_low), fvg_width, htf_fvg_high - htf_fvg_low,
                                 facecolor=htf_fvg_color, alpha=0.3, 
                                 edgecolor=htf_fvg_color, linewidth=2,
                                 label=f'HTF {htf_fvg_type.title()} FVG'))
        
        # Mark trade levels
        entry_x = entry_htf_idx - start_idx
        
        ax.axvline(x=entry_x, color='orange', linestyle='--', 
                linewidth=1.5, alpha=0.6, zorder=3)

        entry_time_str = trade['entry_time'].strftime('%m/%d %H:%M')
        price_low = plot_data['low'].min()
        price_range = plot_data['high'].max() - price_low
        annotation_y = price_low + (price_range * 0.05)

        ax.text(entry_x + 2, annotation_y, f'Entry: {entry_time_str}', 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7),
                verticalalignment='bottom', horizontalalignment='left',
                fontsize=9, color='white', fontweight='bold')

        # Entry line
        ax.axhline(y=trade['entry_price'], color='blue', linestyle='-', 
                  linewidth=2, alpha=0.8, label='Entry Price')
        ax.scatter(entry_x, trade['entry_price'], color='blue', 
                  marker='o', s=120, zorder=5, label='Entry')
        
        # Stop loss and take profit lines
        ax.axhline(y=trade['stop_loss'], color='red', linestyle='--', 
                  linewidth=2, alpha=0.7, label='Stop Loss')
        ax.axhline(y=trade['take_profit'], color='green', linestyle='--', 
                  linewidth=2, alpha=0.7, label='Take Profit')
        
        # Price labels
        chart_width = len(plot_data)
        price_range = plot_data['high'].max() - plot_data['low'].min()
        
        ax.text(chart_width * 0.02, trade['entry_price'], 
               f"Entry: ${trade['entry_price']:.0f}", 
               verticalalignment='center', fontsize=14, fontweight='bold', color='white',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='blue', alpha=0.9, edgecolor='white'))
        
        ax.text(chart_width * 0.85, trade['stop_loss'] + price_range * 0.01, 
               f"Stop Loss: ${trade['stop_loss']:.0f}", 
               verticalalignment='bottom', fontsize=14, fontweight='bold', color='white',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='red', alpha=0.9, edgecolor='white'))
        
        ax.text(chart_width * 0.85, trade['take_profit'] - price_range * 0.01, 
               f"Take Profit: ${trade['take_profit']:.0f}", 
               verticalalignment='top', fontsize=14, fontweight='bold', color='white',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='green', alpha=0.9, edgecolor='white'))
        
        # HTF trend analysis
        htf_bias = setup_details.get('htf_bias', 'neutral')
        
        htf_stats_text = f"HTF Timeframe: {config['htf_timeframe']}\n"
        htf_stats_text += f"HTF Bias: {htf_bias.title()}\n"
        htf_stats_text += f"Trend: {htf_bias.title()}\n"
        htf_stats_text += f"Signal: {trade['signal_type'].upper()}\n"
        htf_stats_text += f"R-Multiple: {trade['r_outcome']:.2f}R"
        
        trend_color = 'green' if htf_bias == 'bullish' else 'red' if htf_bias == 'bearish' else 'gray'
        
        ax.text(0.02, 0.98, htf_stats_text, transform=ax.transAxes,
                bbox=dict(boxstyle='round,pad=0.5', facecolor=trend_color, 
                         edgecolor='black', alpha=0.2),
                verticalalignment='top', fontsize=10)
        
        # Legend
        ax.legend(loc='upper right', fontsize=10, 
                 bbox_to_anchor=(0.98, 0.98), framealpha=0.9)
        
        # Title
        title = f"Trade #{trade_num} - {direction} - HTF Context ({config['htf_timeframe']})"
        ax.set_title(title, fontsize=16, pad=20)
        
        # Format axes
        ax.set_ylabel('Price ($)', fontsize=12)
        ax.set_xlabel('Time', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # X-axis labels
        date_labels = []
        date_positions = []
        step = max(1, len(plot_data) // 8)
        for i in range(0, len(plot_data), step):
            date_labels.append(plot_data.index[i].strftime('%m/%d'))
            date_positions.append(i)
        
        ax.set_xticks(date_positions)
        ax.set_xticklabels(date_labels, rotation=45)
        
        plt.tight_layout()
        
        # Save HTF chart
        filename_htf = f'charts/trade_{trade_num}_htf.png'
        plt.savefig(filename_htf, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ HTF chart saved: {filename_htf}")
        
    except Exception as e:
        print(f"   ⚠️ Error plotting HTF chart {trade_num}: {e}")
        import traceback
        traceback.print_exc()


# ==================== MAIN EXECUTION ====================

def run_ict_system():

    print(f"\n🚀 Starting ICT Analysis using FIXED framework...")
    
    try:


        # Load and prepare data
        m1_data = load_and_prepare_data(config['candle_data_path'])
        if m1_data is None:
            return None
        
       

        # Filter date range
        m1_data = m1_data[config['backtest_start']:config['backtest_end']]
        print(f"📅 Using {len(m1_data):,} candles for analysis")
        
        # Resample to LTF
        ltf_data = resample_data(m1_data, config['ltf_timeframe'])
        print(f"✅ Created {len(ltf_data):,} {config['ltf_timeframe']} candles")
        



        # Generate signals using FIXED functions
        signals = generate_all_signal_types_with_special_patterns(ltf_data)  # FIXED!
        print(f"✅ Total signals generated: {len(signals)}")
        
        # Execute trades
        trades = execute_trades_debug_improved(signals, ltf_data, m1_data)
        print(f"\n✅ Executed {len(trades)} trades from {len(signals)} signals")
        
        # Display results
        print(f"\n{'='*70}")
        print(f"📊 ICT TRADING SYSTEM RESULTS (COMPLETELY FIXED)")
        print(f"{'='*70}")
        
        if len(trades) > 0:
            # Calculate metrics
            win_rate = len([t for t in trades if not t['hit_stop']]) / len(trades)
            avg_r = np.mean([t['r_outcome'] for t in trades])
            total_r = sum([t['r_outcome'] for t in trades])
            
            print(f"\n💰 Performance Summary:")
            print(f"   Total Trades: {len(trades)}")
            print(f"   Win Rate: {win_rate:.1%}")
            print(f"   Average R: {avg_r:.3f}")
            print(f"   Total R: {total_r:.1f}")
            print(f"   Final Balance: ${trades[-1]['balance']:.2f}")
            print(f"   ROI: {((trades[-1]['balance'] - config['starting_balance']) / config['starting_balance'] * 100):.2f}%")
            
            # Generate charts using FIXED plotting function
            if config['plot_charts']:
                print(f"\n📊 Generating FIXED Charts...")
                charts_to_plot = min(len(trades), config['max_charts'])
                for i, trade in enumerate(trades[:charts_to_plot]):
                    plot_enhanced_ict_chart_FIXED(trade, ltf_data, i + 1)  # FIXED!
            
            # Generate CSVs
            if config.get('save_individual_trades', True):

                generate_csv_results_enhanced(trades, {})
            
            if config.get('save_summary_metrics', True):
                generate_summary_csv_FIXED(trades, signals, config)
        
        else:
            print("\n⚠️ No trades executed - check your strategy configurations.")
        
        return trades
        
    except Exception as e:
        print(f"\n❌ Critical error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


# ==================== CSV GENERATION ====================



def generate_summary_csv_FIXED(trades: List[Dict], signals: List, config: Dict):
    """Enhanced summary with additional metrics while keeping original structure"""
    if not trades:
        print("   ⚠️ No trades to summarize")
        return
    
    # Determine which strategies were actually used
    active_strategies = []
    if config.get('generate_raid_fvg', False):
        active_strategies.append('RAID_FVG')
    if config.get('generate_displacement_fvg', False):
        active_strategies.append('DISPLACEMENT_FVG')
    if config.get('generate_fvg_only', False):
        active_strategies.append('FVG_ONLY')
    if config.get('generate_ict_sequence', False):
        active_strategies.append('ICT_SEQUENCE')
    if config.get('generate_liquidity_trap', False):
        active_strategies.append('LIQUIDITY_TRAP')
    if config.get('generate_fake_fvg_fade', False):
        active_strategies.append('FAKE_FVG_FADE')
    
    primary_strategy = active_strategies[0] if active_strategies else 'UNKNOWN'
    all_strategies = ', '.join(active_strategies)

    # Basic trade metrics
    total_trades = len(trades)
    wins = len([t for t in trades if not t['hit_stop']])
    losses = total_trades - wins
    win_rate = wins / total_trades if total_trades > 0 else 0
    
    # NEW: Direction breakdown
    long_trades = len([t for t in trades if t['direction'] == 'bullish'])
    short_trades = len([t for t in trades if t['direction'] == 'bearish'])
    
    # NEW: Calculate average holding time
    holding_times = []
    for trade in trades:
        holding_time = (trade['exit_time'] - trade['entry_time']).total_seconds()
        holding_times.append(holding_time)
    
    avg_holding_seconds = np.mean(holding_times) if holding_times else 0
    avg_holding_minutes = int(avg_holding_seconds // 60)
    avg_holding_remainder_seconds = int(avg_holding_seconds % 60)
    avg_holding_formatted = f"{avg_holding_minutes}m {avg_holding_remainder_seconds}s"
    
    # NEW: Calculate losing streak
    max_losing_streak = 0
    current_losing_streak = 0
    
    for trade in trades:
        if trade['hit_stop']:  # Loss
            current_losing_streak += 1
            max_losing_streak = max(max_losing_streak, current_losing_streak)
        else:  # Win
            current_losing_streak = 0
    
    # NEW: Find largest win and loss
    pnls = [t['net_pnl'] for t in trades]
    largest_win = max(pnls) if pnls else 0
    largest_loss = min(pnls) if pnls else 0
    
    # R-multiple analysis
    r_outcomes = [t['r_outcome'] for t in trades]
    avg_r = np.mean(r_outcomes)
    total_r = sum(r_outcomes)
    
    # Financial metrics
    starting_balance = config['starting_balance']
    final_balance = trades[-1]['balance'] if trades else starting_balance
    total_pnl = final_balance - starting_balance
    roi_pct = (total_pnl / starting_balance) * 100
    
    # Strategy breakdown
    strategy_counts = {}
    for trade in trades:
        signal_type = trade.get('signal_type', 'unknown')
        if signal_type not in strategy_counts:
            strategy_counts[signal_type] = {'total': 0, 'wins': 0, 'total_r': 0}
        
        strategy_counts[signal_type]['total'] += 1
        strategy_counts[signal_type]['total_r'] += trade['r_outcome']
        
        if not trade['hit_stop']:
            strategy_counts[signal_type]['wins'] += 1
    
    # Raid FVG specific metrics
    raid_fvg_trades = [t for t in trades if t.get('signal_type', '') == 'raid_fvg']
    raid_fvg_count = len(raid_fvg_trades)
    
    if raid_fvg_count > 0:
        raid_fvg_wins = len([t for t in raid_fvg_trades if not t['hit_stop']])
        raid_fvg_win_rate = raid_fvg_wins / raid_fvg_count
        raid_fvg_avg_r = np.mean([t['r_outcome'] for t in raid_fvg_trades])
        raid_fvg_total_r = sum([t['r_outcome'] for t in raid_fvg_trades])
    else:
        raid_fvg_win_rate = 0
        raid_fvg_avg_r = 0
        raid_fvg_total_r = 0
    
    # Get risk mode for filename
    risk_mode = config.get('risk_management_mode', 'compounded')
    
    # Create summary data with EXISTING + NEW metrics
    summary_data = {
        # EXISTING METRICS
        'analysis_date': [pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')],
        'backtest_period': [f"{config['backtest_start']} to {config['backtest_end']}"],
        'timeframe': [config['ltf_timeframe']],
        'total_trades': [total_trades],
        'total_signals_generated': [len(signals)],
        'win_rate': [round(win_rate, 3)],
        'avg_r': [round(avg_r, 3)],
        'total_r': [round(total_r, 1)],
        
        # Financial metrics
        'starting_balance': [starting_balance],
        'final_balance': [round(final_balance, 2)],
        'total_pnl': [round(total_pnl, 2)],
        'roi_pct': [round(roi_pct, 2)],
        
        # Strategy information
        'primary_strategy': [primary_strategy],
        'all_strategies_enabled': [all_strategies],
        
        # Config summary
        'risk_per_trade_pct': [config.get('risk_per_trade', 0.45)],
        'risk_management_mode': [risk_mode],
        'max_trades_per_day': [config.get('max_trades_per_day', 10)],
        'configs_summary': [f"HTF:{config.get('use_htf_bias', False)}, TrendFilter:{config.get('use_trend_filter', False)}, Risk:{risk_mode}"],
        
        # NEW METRICS ADDED
        'long_trades': [long_trades],
        'short_trades': [short_trades],
        'avg_holding_time': [avg_holding_formatted],
        'win_trades': [wins],
        'lose_trades': [losses],
        'max_losing_streak': [max_losing_streak],
        'largest_loss': [round(largest_loss, 2)],
        'largest_win': [round(largest_win, 2)],
        
        # Additional new metrics
        'avg_win': [round(np.mean([t['net_pnl'] for t in trades if not t['hit_stop']]), 2)] if wins > 0 else [0],
        'avg_loss': [round(np.mean([t['net_pnl'] for t in trades if t['hit_stop']]), 2)] if losses > 0 else [0],
        'profit_factor': [round(abs(sum([t['net_pnl'] for t in trades if not t['hit_stop']]) / 
                                   sum([t['net_pnl'] for t in trades if t['hit_stop']])), 2)] if losses > 0 else [0],

    }
    
    # Save summary
    summary_df = pd.DataFrame(summary_data)
    filename = f'ict_summary_BTC.csv'
    summary_df.to_csv(filename, index=False)
    
    print(f"\n✅ Enhanced summary saved: {filename}")
    
    # Print detailed breakdown
    print(f"\n📋 Strategy Performance:")
    for strategy, data in strategy_counts.items():
        win_rate_strategy = data['wins'] / data['total'] if data['total'] > 0 else 0
        avg_r_strategy = data['total_r'] / data['total'] if data['total'] > 0 else 0
        print(f"   • {strategy}: {data['total']} trades, {win_rate_strategy:.1%} WR, {avg_r_strategy:.2f}R avg")
    
    # Print new metrics
    print(f"\n📊 Additional Metrics:")
    print(f"   • Long/Short: {long_trades}/{short_trades}")
    print(f"   • Win/Loss: {wins}/{losses}")
    print(f"   • Avg Holding Time: {avg_holding_formatted}")
    print(f"   • Max Losing Streak: {max_losing_streak}")
    print(f"   • Largest Win: ${largest_win:.2f}")
    print(f"   • Largest Loss: ${largest_loss:.2f}")
   








def extract_actual_configs_used(trade: Dict, setup: Dict) -> str:
    """Extract the actual configs that affected this trade"""
    configs = []
    
    # Core strategy configs
    configs.append(f"signal_type={trade.get('signal_type', 'unknown')}")
    configs.append(f"stop_method={setup.get('stop_loss_method', 'unknown')}")
    configs.append(f"tp_method={setup.get('take_profit_method', 'unknown')}")
    
    # HTF configs
    if config.get('use_htf_bias', False):
        configs.append(f"htf_bias={setup.get('htf_bias', 'unknown')}")
    
    # Risk configs
    configs.append(f"risk_pct={config.get('risk_per_trade', 0.45)}")
    configs.append(f"min_rr={config.get('min_rr_ratio', 1.5)}")
    
    # FVG configs
    if 'fvg' in trade.get('signal_type', '').lower():
        configs.append(f"fvg_size_pct={config.get('fvg_min_size_pct', 2.0)}")
        configs.append(f"body_ratio={config.get('body_ratio_threshold', 0.7)}")
    
    return "; ".join(configs)




def generate_csv_results_enhanced(trades: List[Dict], signal_analysis: Dict):
    """EXACT SAME structure, just add risk mode to filename"""
    try:
     
       
        
        trade_records = []
        for i, trade in enumerate(trades):
            setup = trade.get('setup_details', {})
            

            stop_method = setup.get('stop_loss_method', config.get('stop_loss_method', 'fvg_based'))
            configs_used = extract_actual_configs_used(trade, setup)
            
            # EXACT SAME RECORD STRUCTURE
            record = {
                'trade_num': i + 1,
                'entry_time': trade['entry_time'],
                'exit_time': trade['exit_time'],
                'pair': config['crypto_symbol'] + 'USDT',
                'signal_type': trade['signal_type'],
                'direction': 'long' if trade['direction'] == 'bullish' else 'short',
                'htf_bias': setup.get('htf_bias', 'neutral'),
                'htf_bias_aligned': setup.get('htf_bias_aligned', True),
                'entry_price': round(trade['entry_price'], 2),
                'exit_price': round(trade['exit_price'], 2),
                'stop_loss': round(trade['stop_loss'], 2),
                'take_profit': round(trade['take_profit'], 2),
                'stop_distance': round(abs(trade['entry_price'] - trade['stop_loss']), 2),
                'stop_distance_pct': round((abs(trade['entry_price'] - trade['stop_loss']) / trade['entry_price']) * 100, 2),
                'risk_amount': round(trade['risk_amount'], 2),
                'position_size': round(trade['position_size_btc'], 4),
                'pnl': round(trade['net_pnl'], 2),
                'r_outcome': round(trade['r_outcome'], 2),
                'rr_ratio': round(trade.get('actual_rr', 0), 2),
                'cumulative_r': round(sum([t['r_outcome'] for t in trades[:i+1]]), 2),
                'ending_balance': round(trade['balance'], 2),
                'win_lose': 'WIN' if not trade['hit_stop'] else 'LOSS',
                'fees': round(trade['total_fees'], 2),
                'fee_pct_of_risk': round(trade.get('fee_pct_of_risk', 0), 2),
                'stop_method': stop_method,
                'take_profit_method': setup.get('take_profit_method', config.get('take_profit_method', 'fixed_r')),
                'configs_used': configs_used
            }
            trade_records.append(record)
        
        # Save with proper format (ONLY CHANGE: filename includes risk mode)
        trades_df = pd.DataFrame(trade_records)
        
 
        filename = f'ict_trades_BTC.csv'
        trades_df.to_csv(filename, index=False)
        print(f"   ✅ Trades CSV saved: {filename}")
     
        
    except Exception as e:
        print(f"   ⚠️ Error saving CSV: {e}")

# ==================== EXECUTE ====================

if __name__ == "__main__":
    print("\n" + "="*70)
    print("💎 ICT Trading System v8.2 - Complete Implementation")

    print("="*70)
    

    



    results = run_ict_system()

